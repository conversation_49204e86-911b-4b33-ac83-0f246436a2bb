<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke }   from '@tauri-apps/api/core';
  import Plotly       from 'plotly.js-dist';
  import { ResizeObserver } from '@juggle/resize-observer';

  /* ---------------- State ---------------- */
  let rows: { x: string; y: string }[] = [
    { x: '1', y: '2' },
    { x: '2', y: '4' },
    { x: '3', y: '5' }
  ];
  let regression: { slope: number; intercept: number } | null = null;
  let errorMsg = '';

  // chart containers
  let chart2dDiv: HTMLDivElement;
  let chart3dDiv: HTMLDivElement;
  let chart2dContainer: HTMLDivElement;
  let chart3dContainer: HTMLDivElement;
  let resizeHandle: HTMLDivElement;

  /* ---------------- Helpers ---------------- */
  const addRow    = () => rows = [...rows, { x: '', y: '' }];
  const deleteRow = (i: number) => rows = rows.filter((_, idx) => idx !== i);

  function attachResize(div: HTMLElement) {
    const ro = new ResizeObserver(() => {
      // Use requestAnimationFrame to ensure DOM has updated
      requestAnimationFrame(() => {
        if (div && div.parentElement && (div as any)._fullLayout) {
          const container = div.parentElement;
          const width = container.offsetWidth;
          const height = container.offsetHeight;

          if (width > 0 && height > 0) {
            try {
              Plotly.relayout(div, {
                width: width,
                height: height
              });
            } catch (error) {
              console.log('Resize error:', error);
              // Fallback to Plots.resize
              Plotly.Plots.resize(div);
            }
          }
        }
      });
    });
    ro.observe(div.parentElement || div);
    return ro;
  }

  function setupResizeHandle() {
    if (!resizeHandle || !chart2dContainer || !chart3dContainer) {
      console.log('Missing elements for resize setup');
      return;
    }

    let isResizing = false;
    let startY = 0;
    let startHeight2d = 0;
    let startHeight3d = 0;

    const onMouseDown = (e: MouseEvent) => {
      e.preventDefault();
      isResizing = true;
      startY = e.clientY;

      // Get current heights
      const rect2d = chart2dContainer.getBoundingClientRect();
      const rect3d = chart3dContainer.getBoundingClientRect();
      startHeight2d = rect2d.height;
      startHeight3d = rect3d.height;

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      document.body.style.cursor = 'row-resize';
      document.body.style.userSelect = 'none';
    };

    const onMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      e.preventDefault();

      const deltaY = e.clientY - startY;
      const totalHeight = startHeight2d + startHeight3d;
      const minHeight = 200;

      let newHeight2d = startHeight2d + deltaY;
      newHeight2d = Math.max(minHeight, Math.min(totalHeight - minHeight, newHeight2d));
      const newHeight3d = totalHeight - newHeight2d;

      // Set explicit heights and disable flex
      chart2dContainer.style.height = `${newHeight2d}px`;
      chart2dContainer.style.flex = 'none';
      chart3dContainer.style.height = `${newHeight3d}px`;
      chart3dContainer.style.flex = 'none';

      // Force chart resize
      requestAnimationFrame(() => {
        if (chart2dDiv && chart2dContainer) {
          Plotly.relayout(chart2dDiv, {
            width: chart2dContainer.offsetWidth,
            height: chart2dContainer.offsetHeight
          });
        }
        if (chart3dDiv && chart3dContainer) {
          Plotly.relayout(chart3dDiv, {
            width: chart3dContainer.offsetWidth,
            height: chart3dContainer.offsetHeight
          });
        }
      });
    };

    const onMouseUp = () => {
      isResizing = false;
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    resizeHandle.addEventListener('mousedown', onMouseDown);

    return () => {
      resizeHandle?.removeEventListener('mousedown', onMouseDown);
    };
  }

  async function runRegression() {
    errorMsg = '';
    regression = null;

    const xs: number[] = [];
    const ys: number[] = [];
    for (const { x, y } of rows) {
      const xv = parseFloat(x);
      const yv = parseFloat(y);
      if (Number.isFinite(xv) && Number.isFinite(yv)) {
        xs.push(xv);
        ys.push(yv);
      }
    }
    if (!xs.length) {
      errorMsg = 'Enter at least one (x,y) pair';
      return;
    }

    console.log('Sending data to Rust:', { x_values: xs, y_values: ys });

    try {
      const result = await invoke('compute_regression', {
        input: { x_values: xs, y_values: ys }
      }) as { x_values:number[]; y_values:number[]; slope:number; intercept:number };

      regression = { slope: result.slope, intercept: result.intercept };
      drawCharts(result);
    } catch (err) {
      console.error('Detailed error:', err);
      console.error('Error type:', typeof err);
      console.error('Error message:', err?.message || err);
      errorMsg = typeof err === 'string' ? err : (err?.message || 'Regression failed');
    }
  }

  function drawCharts(r: { x_values:number[]; y_values:number[]; slope:number; intercept:number }) {
    const lineY = r.x_values.map(x => r.intercept + r.slope * x);

    // ---------- 2‑D ----------
    const containerWidth = chart2dDiv.parentElement?.offsetWidth || 800;
    const containerHeight = chart2dDiv.parentElement?.offsetHeight || 400;

    Plotly.newPlot(chart2dDiv,
      [
        { x: r.x_values, y: r.y_values, mode:'markers', type:'scatter', name:'Data', marker:{ size:8 } },
        { x: r.x_values, y: lineY,      mode:'lines',   type:'scatter', name:'Fit',  line:{ width:3 } }
      ],
      {
        title:'2‑D Regression',
        xaxis:{title:'x'},
        yaxis:{title:'y'},
        width: containerWidth,
        height: containerHeight,
        margin: { l: 60, r: 30, t: 60, b: 60 },
        autosize: true
      },
      {
        responsive: true,
        displayModeBar: false
      }
    );

    // ---------- 3‑D ----------
    const zeros = r.x_values.map(() => 0);
    const container3dWidth = chart3dDiv.parentElement?.offsetWidth || 800;
    const container3dHeight = chart3dDiv.parentElement?.offsetHeight || 400;

    Plotly.newPlot(chart3dDiv,
      [
        { x:r.x_values, y:r.y_values, z:zeros, mode:'markers', type:'scatter3d', name:'Data', marker:{ size:4 } },
        { x:r.x_values, y:lineY,      z:zeros, mode:'lines',   type:'scatter3d', name:'Fit',  line:{ width:6 } }
      ],
      {
        title:'3‑D View (z=0)',
        scene:{
          xaxis:{title:'x'},
          yaxis:{title:'y'},
          zaxis:{title:'z'},
          camera: {
            eye: { x: 1.5, y: 1.5, z: 1.5 }
          }
        },
        width: container3dWidth,
        height: container3dHeight,
        margin: { l: 0, r: 0, t: 60, b: 0 },
        autosize: true
      },
      {
        responsive: true,
        displayModeBar: false
      }
    );

    // Force resize after plots are created
    setTimeout(forceChartResize, 50);
  }

  function forceChartResize() {
    // Force charts to resize to their containers
    setTimeout(() => {
      if (chart2dDiv && chart2dDiv.parentElement) {
        const container = chart2dDiv.parentElement;
        const width = container.offsetWidth;
        const height = container.offsetHeight;
        console.log('2D Chart container size:', width, 'x', height);

        if (width > 0 && height > 0 && (chart2dDiv as any)._fullLayout) {
          try {
            Plotly.relayout(chart2dDiv, {
              width: width,
              height: height
            });
          } catch (error) {
            console.log('2D resize error:', error);
            Plotly.Plots.resize(chart2dDiv);
          }
        }
      }
      if (chart3dDiv && chart3dDiv.parentElement) {
        const container = chart3dDiv.parentElement;
        const width = container.offsetWidth;
        const height = container.offsetHeight;
        console.log('3D Chart container size:', width, 'x', height);

        if (width > 0 && height > 0 && (chart3dDiv as any)._fullLayout) {
          try {
            Plotly.relayout(chart3dDiv, {
              width: width,
              height: height
            });
          } catch (error) {
            console.log('3D resize error:', error);
            Plotly.Plots.resize(chart3dDiv);
          }
        }
      }
    }, 100);
  }

  onMount(() => {
    console.log('Component mounted');
    console.log('chart2dDiv:', chart2dDiv);
    console.log('chart3dDiv:', chart3dDiv);
    console.log('chart2dContainer:', chart2dContainer);
    console.log('chart3dContainer:', chart3dContainer);
    console.log('resizeHandle:', resizeHandle);

    runRegression();            // initial plot
    attachResize(chart2dDiv);   // make charts auto‑resize with container
    attachResize(chart3dDiv);
    setupResizeHandle();        // setup drag-to-resize functionality

    // Force initial resize after a short delay to ensure DOM is ready
    setTimeout(forceChartResize, 200);

    // Add window resize listener for when the entire app window is resized
    const handleWindowResize = () => {
      setTimeout(forceChartResize, 100);
    };

    window.addEventListener('resize', handleWindowResize);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  });
</script>

<!-- ---------------- Layout ---------------- -->
<div class="h-screen bg-gray-50 font-sans" style="position: relative; overflow: hidden;">
  <!-- Sidebar: Data Entry -->
  <section class="border-r border-gray-200 p-4 overflow-auto" style="position: absolute; left: 0; top: 0; width: 256px; height: 100%; background: #f9fafb;">
    <h1 class="text-2xl font-semibold text-gray-700 mb-4">Interactive Regression</h1>

    <table class="w-full text-sm mb-4">
      <thead class="bg-gray-100 sticky top-0 shadow-sm">
        <tr><th class="p-2">x</th><th class="p-2">y</th><th class="p-2"></th></tr>
      </thead>
      <tbody>
        {#each rows as row, i}
          <tr class="border-b">
            <td class="p-1"><input class="w-full px-2 py-1 rounded border" bind:value={row.x} /></td>
            <td class="p-1"><input class="w-full px-2 py-1 rounded border" bind:value={row.y} /></td>
            <td class="p-1 text-center"><button class="text-red-500" on:click={() => deleteRow(i)}>🗑</button></td>
          </tr>
        {/each}
      </tbody>
    </table>

    <div class="flex gap-2 mb-4">
      <button class="flex-1 bg-green-600 text-white py-1 rounded shadow" on:click={addRow}>+ Row</button>
      <button class="flex-1 bg-blue-600  text-white py-1 rounded shadow" on:click={runRegression}>Run ▶</button>
    </div>

    {#if errorMsg}
      <p class="text-red-600 text-sm mb-2">{errorMsg}</p>
    {/if}

    {#if regression}
      <div class="text-sm text-gray-700">
        <p>Slope m: <span class="font-semibold">{regression.slope.toFixed(4)}</span></p>
        <p>Intercept b: <span class="font-semibold">{regression.intercept.toFixed(4)}</span></p>
      </div>
    {/if}
  </section>

  <!-- Main: Charts -->
  <section class="p-4 overflow-hidden" style="position: absolute; left: 256px; top: 0; right: 0; height: 100%; display: flex; flex-direction: column;">
    <!-- 2D Chart -->
    <div bind:this={chart2dContainer} style="flex: 1; width: 100%; min-height: 250px; display: flex; flex-direction: column;">
      <div bind:this={chart2dDiv} style="width: 100%; height: 100%; background: white; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); position: relative;"></div>
    </div>

    <!-- Resizable divider -->
    <div bind:this={resizeHandle} style="background: #cbd5e0; height: 12px; cursor: row-resize; margin: 8px 0; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
      <div style="width: 40px; height: 3px; background: #64748b; border-radius: 2px;"></div>
    </div>

    <!-- 3D Chart -->
    <div bind:this={chart3dContainer} style="flex: 1; width: 100%; min-height: 250px; display: flex; flex-direction: column;">
      <div bind:this={chart3dDiv} style="width: 100%; height: 100%; background: white; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); position: relative;"></div>
    </div>
  </section>
</div>

<style>
  /* Custom scrollbar for sidebar */
  section:first-child {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
  }

  section:first-child::-webkit-scrollbar {
    width: 6px;
  }

  section:first-child::-webkit-scrollbar-track {
    background: #f7fafc;
  }

  section:first-child::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
  }

  section:first-child::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }

  /* Ensure charts take full space */
  :global(.plotly-graph-div) {
    width: 100% !important;
    height: 100% !important;
  }
</style>
