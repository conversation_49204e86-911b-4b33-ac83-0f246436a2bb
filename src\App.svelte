<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke }   from '@tauri-apps/api/core';
  import Plotly       from 'plotly.js-dist';
  import { ResizeObserver } from '@juggle/resize-observer';

  /* ---------------- State ---------------- */
  let rows: { x: string; y: string }[] = [
    { x: '1', y: '2' },
    { x: '2', y: '4' },
    { x: '3', y: '5' }
  ];
  let regression: { slope: number; intercept: number } | null = null;
  let errorMsg = '';

  // chart containers
  let chart2dDiv: HTMLDivElement;
  let chart3dDiv: HTMLDivElement;
  let chart2dContainer: HTMLDivElement;
  let chart3dContainer: HTMLDivElement;
  let resizeHandle: HTMLDivElement;

  /* ---------------- Helpers ---------------- */
  const addRow    = () => rows = [...rows, { x: '', y: '' }];
  const deleteRow = (i: number) => rows = rows.filter((_, idx) => idx !== i);

  function attachResize(div: HTMLElement) {
    const ro = new ResizeObserver(() => {
      // Use requestAnimationFrame to ensure DOM has updated
      requestAnimationFrame(() => {
        if (div && div.offsetWidth > 0 && div.offsetHeight > 0) {
          Plotly.Plots.resize(div);
        }
      });
    });
    ro.observe(div);
    return ro;
  }

  function setupResizeHandle() {
    if (!resizeHandle || !chart2dContainer || !chart3dContainer) return;

    let isResizing = false;
    let startY = 0;
    let startHeight2d = 0;
    let startHeight3d = 0;

    const onMouseDown = (e: MouseEvent) => {
      isResizing = true;
      startY = e.clientY;
      startHeight2d = chart2dContainer.offsetHeight;
      startHeight3d = chart3dContainer.offsetHeight;

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      document.body.style.cursor = 'row-resize';
      document.body.style.userSelect = 'none';
    };

    const onMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaY = e.clientY - startY;
      const totalHeight = startHeight2d + startHeight3d;
      const newHeight2d = Math.max(200, Math.min(totalHeight - 200, startHeight2d + deltaY));
      const newHeight3d = totalHeight - newHeight2d;

      chart2dContainer.style.height = `${newHeight2d}px`;
      chart3dContainer.style.height = `${newHeight3d}px`;
      chart2dContainer.style.flexGrow = '0';
      chart3dContainer.style.flexGrow = '0';

      // Trigger Plotly resize with a slight delay to ensure DOM updates
      requestAnimationFrame(() => {
        if (chart2dDiv && chart2dDiv.offsetWidth > 0) {
          Plotly.Plots.resize(chart2dDiv);
        }
        if (chart3dDiv && chart3dDiv.offsetWidth > 0) {
          Plotly.Plots.resize(chart3dDiv);
        }
      });
    };

    const onMouseUp = () => {
      isResizing = false;
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    resizeHandle.addEventListener('mousedown', onMouseDown);

    return () => {
      resizeHandle?.removeEventListener('mousedown', onMouseDown);
    };
  }

  async function runRegression() {
    errorMsg = '';
    regression = null;

    const xs: number[] = [];
    const ys: number[] = [];
    for (const { x, y } of rows) {
      const xv = parseFloat(x);
      const yv = parseFloat(y);
      if (Number.isFinite(xv) && Number.isFinite(yv)) {
        xs.push(xv);
        ys.push(yv);
      }
    }
    if (!xs.length) {
      errorMsg = 'Enter at least one (x,y) pair';
      return;
    }

    console.log('Sending data to Rust:', { x_values: xs, y_values: ys });

    try {
      const result = await invoke('compute_regression', {
        input: { x_values: xs, y_values: ys }
      }) as { x_values:number[]; y_values:number[]; slope:number; intercept:number };

      regression = { slope: result.slope, intercept: result.intercept };
      drawCharts(result);
    } catch (err) {
      console.error('Detailed error:', err);
      console.error('Error type:', typeof err);
      console.error('Error message:', err?.message || err);
      errorMsg = typeof err === 'string' ? err : (err?.message || 'Regression failed');
    }
  }

  function drawCharts(r: { x_values:number[]; y_values:number[]; slope:number; intercept:number }) {
    const lineY = r.x_values.map(x => r.intercept + r.slope * x);

    // ---------- 2‑D ----------
    Plotly.newPlot(chart2dDiv,
      [
        { x: r.x_values, y: r.y_values, mode:'markers', type:'scatter', name:'Data', marker:{ size:8 } },
        { x: r.x_values, y: lineY,      mode:'lines',   type:'scatter', name:'Fit',  line:{ width:3 } }
      ],
      {
        title:'2‑D Regression',
        xaxis:{title:'x'},
        yaxis:{title:'y'},
        margin: { l: 50, r: 20, t: 50, b: 50 },
        autosize: true
      },
      {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d']
      }
    );

    // ---------- 3‑D ----------
    const zeros = r.x_values.map(() => 0);
    Plotly.newPlot(chart3dDiv,
      [
        { x:r.x_values, y:r.y_values, z:zeros, mode:'markers', type:'scatter3d', name:'Data', marker:{ size:4 } },
        { x:r.x_values, y:lineY,      z:zeros, mode:'lines',   type:'scatter3d', name:'Fit',  line:{ width:6 } }
      ],
      {
        title:'3‑D View (z=0)',
        scene:{
          xaxis:{title:'x'},
          yaxis:{title:'y'},
          zaxis:{title:'z'},
          camera: {
            eye: { x: 1.5, y: 1.5, z: 1.5 }
          }
        },
        margin: { l: 0, r: 0, t: 50, b: 0 },
        autosize: true
      },
      {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: ['pan3d', 'orbitRotation']
      }
    );

    // Force resize after plots are created
    setTimeout(forceChartResize, 50);
  }

  function forceChartResize() {
    // Force charts to resize to their containers
    setTimeout(() => {
      if (chart2dDiv && chart2dDiv.offsetWidth > 0) {
        Plotly.Plots.resize(chart2dDiv);
      }
      if (chart3dDiv && chart3dDiv.offsetWidth > 0) {
        Plotly.Plots.resize(chart3dDiv);
      }
    }, 100);
  }

  onMount(() => {
    runRegression();            // initial plot
    attachResize(chart2dDiv);   // make charts auto‑resize with container
    attachResize(chart3dDiv);
    setupResizeHandle();        // setup drag-to-resize functionality

    // Force initial resize after a short delay to ensure DOM is ready
    setTimeout(forceChartResize, 200);

    // Add window resize listener for when the entire app window is resized
    const handleWindowResize = () => {
      setTimeout(forceChartResize, 100);
    };

    window.addEventListener('resize', handleWindowResize);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  });
</script>

<!-- ---------------- Layout ---------------- -->
<div class="flex h-screen overflow-hidden bg-gray-50 font-sans">
  <!-- Sidebar: Data Entry -->
  <section class="w-80 border-r border-gray-200 p-4 overflow-auto flex-shrink-0">
    <h1 class="text-2xl font-semibold text-gray-700 mb-4">Interactive Regression</h1>

    <table class="w-full text-sm mb-4">
      <thead class="bg-gray-100 sticky top-0 shadow-sm">
        <tr><th class="p-2">x</th><th class="p-2">y</th><th class="p-2"></th></tr>
      </thead>
      <tbody>
        {#each rows as row, i}
          <tr class="border-b">
            <td class="p-1"><input class="w-full px-2 py-1 rounded border" bind:value={row.x} /></td>
            <td class="p-1"><input class="w-full px-2 py-1 rounded border" bind:value={row.y} /></td>
            <td class="p-1 text-center"><button class="text-red-500" on:click={() => deleteRow(i)}>🗑</button></td>
          </tr>
        {/each}
      </tbody>
    </table>

    <div class="flex gap-2 mb-4">
      <button class="flex-1 bg-green-600 text-white py-1 rounded shadow" on:click={addRow}>+ Row</button>
      <button class="flex-1 bg-blue-600  text-white py-1 rounded shadow" on:click={runRegression}>Run ▶</button>
    </div>

    {#if errorMsg}
      <p class="text-red-600 text-sm mb-2">{errorMsg}</p>
    {/if}

    {#if regression}
      <div class="text-sm text-gray-700">
        <p>Slope m: <span class="font-semibold">{regression.slope.toFixed(4)}</span></p>
        <p>Intercept b: <span class="font-semibold">{regression.intercept.toFixed(4)}</span></p>
      </div>
    {/if}
  </section>

  <!-- Main: Charts -->
  <section class="flex-1 flex flex-col p-4 overflow-hidden">
    <!-- 2D Chart -->
    <div bind:this={chart2dContainer} class="flex-1 mb-2 min-h-0">
      <div bind:this={chart2dDiv} class="w-full h-full bg-white rounded-lg shadow-lg overflow-hidden resize-container"></div>
    </div>

    <!-- Resizable divider -->
    <div bind:this={resizeHandle} class="h-2 bg-gray-300 hover:bg-gray-400 cursor-row-resize flex items-center justify-center transition-colors duration-200 rounded">
      <div class="w-8 h-1 bg-gray-500 rounded"></div>
    </div>

    <!-- 3D Chart -->
    <div bind:this={chart3dContainer} class="flex-1 mt-2 min-h-0">
      <div bind:this={chart3dDiv} class="w-full h-full bg-white rounded-lg shadow-lg overflow-hidden resize-container"></div>
    </div>
  </section>
</div>

<style>
  /* ensure Plotly canvas fits its parent */
  .resize-container {
    min-height: 200px;
    position: relative;
  }

  /* Custom scrollbar for sidebar */
  section:first-child {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
  }

  section:first-child::-webkit-scrollbar {
    width: 6px;
  }

  section:first-child::-webkit-scrollbar-track {
    background: #f7fafc;
  }

  section:first-child::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
  }

  section:first-child::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }

  /* Ensure charts take full space and are properly sized */
  :global(.plotly-graph-div) {
    height: 100% !important;
    width: 100% !important;
  }

  /* Ensure chart containers have proper positioning */
  :global(.resize-container .plotly-graph-div) {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
  }
</style>
