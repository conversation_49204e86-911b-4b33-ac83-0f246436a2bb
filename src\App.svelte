<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke }   from '@tauri-apps/api/core';
  import Plotly       from 'plotly.js-dist';
  import { ResizeObserver } from '@juggle/resize-observer';

  /* ---------------- State ---------------- */
  let rows: { x: string; y: string }[] = [
    { x: '1', y: '2' },
    { x: '2', y: '4' },
    { x: '3', y: '5' }
  ];
  let regression: { slope: number; intercept: number } | null = null;
  let errorMsg = '';

  // chart containers
  let chart2dDiv: HTMLDivElement;
  let chart3dDiv: HTMLDivElement;

  /* ---------------- Helpers ---------------- */
  const addRow    = () => rows = [...rows, { x: '', y: '' }];
  const deleteRow = (i: number) => rows = rows.filter((_, idx) => idx !== i);

  function attachResize(div: HTMLElement) {
    const ro = new ResizeObserver(() => Plotly.Plots.resize(div));
    ro.observe(div);
  }

  async function runRegression() {
    errorMsg = '';
    regression = null;

    const xs: number[] = [];
    const ys: number[] = [];
    for (const { x, y } of rows) {
      const xv = parseFloat(x);
      const yv = parseFloat(y);
      if (Number.isFinite(xv) && Number.isFinite(yv)) {
        xs.push(xv);
        ys.push(yv);
      }
    }
    if (!xs.length) {
      errorMsg = 'Enter at least one (x,y) pair';
      return;
    }
    try {
      const result = await invoke('compute_regression', {
        input: { x_values: xs, y_values: ys }
      }) as { x_values:number[]; y_values:number[]; slope:number; intercept:number };

      regression = { slope: result.slope, intercept: result.intercept };
      drawCharts(result);
    } catch (err) {
      errorMsg = typeof err === 'string' ? err : 'Regression failed';
      console.error(err);
    }
  }

  function drawCharts(r: { x_values:number[]; y_values:number[]; slope:number; intercept:number }) {
    const lineY = r.x_values.map(x => r.intercept + r.slope * x);

    // ---------- 2‑D ----------
    Plotly.newPlot(chart2dDiv,
      [
        { x: r.x_values, y: r.y_values, mode:'markers', type:'scatter', name:'Data', marker:{ size:8 } },
        { x: r.x_values, y: lineY,      mode:'lines',   type:'scatter', name:'Fit',  line:{ width:3 } }
      ],
      { title:'2‑D Regression', xaxis:{title:'x'}, yaxis:{title:'y'} },
      { responsive: true }
    );

    // ---------- 3‑D ----------
    const zeros = r.x_values.map(() => 0);
    Plotly.newPlot(chart3dDiv,
      [
        { x:r.x_values, y:r.y_values, z:zeros, mode:'markers', type:'scatter3d', name:'Data', marker:{ size:4 } },
        { x:r.x_values, y:lineY,      z:zeros, mode:'lines',   type:'scatter3d', name:'Fit',  line:{ width:6 } }
      ],
      {
        title:'3‑D View (z=0)',
        scene:{ xaxis:{title:'x'}, yaxis:{title:'y'}, zaxis:{title:'z'} }
      },
      { responsive:true }
    );
  }

  onMount(() => {
    runRegression();            // initial plot
    attachResize(chart2dDiv);   // make charts auto‑resize with container
    attachResize(chart3dDiv);
  });
</script>

<!-- ---------------- Layout ---------------- -->
<div class="flex flex-col md:flex-row h-screen overflow-hidden bg-gray-50 font-sans">
  <!-- Sidebar: Data Entry -->
  <section class="md:w-80 w-full md:border-r border-gray-200 p-4 overflow-auto">
    <h1 class="text-2xl font-semibold text-gray-700 mb-4">Interactive Regression</h1>

    <table class="w-full text-sm mb-4">
      <thead class="bg-gray-100 sticky top-0 shadow-sm">
        <tr><th class="p-2">x</th><th class="p-2">y</th><th class="p-2"></th></tr>
      </thead>
      <tbody>
        {#each rows as row, i}
          <tr class="border-b">
            <td class="p-1"><input class="w-full px-2 py-1 rounded border" bind:value={row.x} /></td>
            <td class="p-1"><input class="w-full px-2 py-1 rounded border" bind:value={row.y} /></td>
            <td class="p-1 text-center"><button class="text-red-500" on:click={() => deleteRow(i)}>🗑</button></td>
          </tr>
        {/each}
      </tbody>
    </table>

    <div class="flex gap-2 mb-4">
      <button class="flex-1 bg-green-600 text-white py-1 rounded shadow" on:click={addRow}>+ Row</button>
      <button class="flex-1 bg-blue-600  text-white py-1 rounded shadow" on:click={runRegression}>Run ▶</button>
    </div>

    {#if errorMsg}
      <p class="text-red-600 text-sm mb-2">{errorMsg}</p>
    {/if}

    {#if regression}
      <div class="text-sm text-gray-700">
        <p>Slope m: <span class="font-semibold">{regression.slope.toFixed(4)}</span></p>
        <p>Intercept b: <span class="font-semibold">{regression.intercept.toFixed(4)}</span></p>
      </div>
    {/if}
  </section>

  <!-- Main: Charts -->
  <section class="flex-1 grid grid-rows-2 gap-4 p-4 overflow-auto">
    <div bind:this={chart2dDiv} class="bg-white rounded-lg shadow-lg resize overflow-hidden"></div>
    <div bind:this={chart3dDiv} class="bg-white rounded-lg shadow-lg resize overflow-hidden"></div>
  </section>
</div>

<style>
  /* ensure resized Plotly canvas fits its parent */
  .resize {
    min-height: 250px;
  }
</style>
