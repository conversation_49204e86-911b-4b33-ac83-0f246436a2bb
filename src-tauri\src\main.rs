// src-tauri/src/main.rs
#![cfg_attr(all(not(debug_assertions), target_os = "windows"), windows_subsystem = "windows")]

use serde::{Deserialize, Serialize};
use tauri::command;

#[derive(Deserialize)]
struct InputData {
    x_values: Vec<f64>,
    y_values: Vec<f64>,
}

#[derive(Serialize)]
struct RegressionResult {
    x_values: Vec<f64>,
    y_values: Vec<f64>,
    slope:     f64,
    intercept: f64,
}

#[command]
fn test_command() -> String {
    "Hello from Rust!".to_string()
}

#[command]
fn compute_regression(input: InputData) -> Result<RegressionResult, String> {
    let n = input.x_values.len();
    if n == 0 || n != input.y_values.len() {
        return Err("x_values and y_values must be the same, non-zero length".into());
    }

    // Σ calculations (all f64 for convenience)
    let n_f        = n as f64;
    let sum_x: f64 = input.x_values.iter().sum();
    let sum_y: f64 = input.y_values.iter().sum();
    let sum_xy: f64 = input
        .x_values
        .iter()
        .zip(&input.y_values)
        .map(|(x, y)| x * y)
        .sum();
    let sum_x2: f64 = input.x_values.iter().map(|x| x * x).sum();

    // Ordinary Least Squares
    let denom = n_f * sum_x2 - sum_x * sum_x;
    if denom.abs() < 1e-12 {
        return Err("Cannot compute regression (denominator ≈ 0)".into());
    }
    let slope     = (n_f * sum_xy - sum_x * sum_y) / denom;
    let intercept = (sum_y - slope * sum_x) / n_f;

    Ok(RegressionResult {
        x_values: input.x_values,
        y_values: input.y_values,
        slope,
        intercept,
    })
}

fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![test_command, compute_regression])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
